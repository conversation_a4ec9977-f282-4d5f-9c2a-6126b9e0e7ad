'use client';
import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Section3() {
  // Header section refs
  const [brandImageRef, brandImageInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [headingRef, headingInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [descriptionRef, descriptionInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [subHeadingRef, subHeadingInView] = useInView({ threshold: 0.1, triggerOnce: true });
  
  // Card refs for first section
  const [card1Ref, card1InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card2Ref, card2InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card3Ref, card3InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card4Ref, card4InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card5Ref, card5InView] = useInView({ threshold: 0.2, triggerOnce: true });
  
  // Video marketing section refs
  const [videoHeadingRef, videoHeadingInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoDescRef, videoDescInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoSubHeadingRef, videoSubHeadingInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [videoImageRef, videoImageInView] = useInView({ threshold: 0.2, triggerOnce: true });
  
  // Video card refs
  const [videoCard1Ref, videoCard1InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoCard2Ref, videoCard2InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoCard3Ref, videoCard3InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoCard4Ref, videoCard4InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [videoCard5Ref, videoCard5InView] = useInView({ threshold: 0.2, triggerOnce: true });

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };
  
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };
  
  const imageScale = {
    hidden: { opacity: 0, scale: 0.92 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };
  
  const staggerCards = {
    hidden: { opacity: 0 },
    visible: (i = 0) => ({
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1 * i,
      },
    }),
  };
  
  const cardItem = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };
  
  const gifAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.5, 
        ease: "easeOut" 
      }
    }
  };

  return (
    <>
      <section className="section-box box-our-track box-our-track-3">
        <div className="container">
          <div className="row">
            {/* Modified for responsive order - Text first on mobile */}
            <div className="col-lg-6 mb-40 order-1 order-lg-2">
              <div className="box-padding-left-50">
                <motion.h2 
                  ref={headingRef}
                  initial="hidden"
                  animate={headingInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="heading-2 mb-20"
                >
                  BRANDING: BE MEMORABLE OR BE FORGOTTEN.
                </motion.h2>
                <motion.p 
                  ref={descriptionRef}
                  initial="hidden"
                  animate={descriptionInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="text-lg neutral-700 mb-40"
                >
                  In an overcrowded market, your brand is your most valuable
                  asset. If people don't remember you, they won't buy from you.
                  Branding isn't just about logos and colors—it's about building
                  a powerful identity that earns trust, creates loyalty, and
                  drives sales.
                </motion.p>
              </div>
            </div>
            
            {/* Image after text on mobile */}
            <div className="col-lg-6 align-items-center mb-40 order-2 order-lg-1">
              <motion.img
                ref={brandImageRef}
                initial="hidden"
                animate={brandImageInView ? "visible" : "hidden"}
                variants={imageScale}
                src="/assets/imgs/page/digital/branding.png"
                alt="Brand development services Hyderabad - Digital marketing agency branding solutions"
              />
            </div>
          </div>
          <div className="mt-50 mb-50">
            <motion.h4
              ref={subHeadingRef}
              initial="hidden"
              animate={subHeadingInView ? "visible" : "hidden"}
              variants={fadeInUp}
            >
              <strong>What We Do:</strong>
            </motion.h4>
            <motion.div 
              className="row mt-50"
              variants={staggerCards}
              initial="hidden"
              animate="visible"
            >
              <div className="col-lg-6 col-sm-6">
                <motion.div 
                  className="card-feature-2"
                  ref={card1Ref}
                  variants={cardItem}
                  initial="hidden"
                  animate={card1InView ? "visible" : "hidden"}
                >
                  <div className="card-image">
                    <motion.img
                      variants={gifAnimation}
                      src="/assets/imgs/page/digitalmarketing/marketing.gif"
                      alt="Digital marketing strategies Hyderabad - Comprehensive marketing solutions"
                    />
                  </div>
                  <motion.div 
                    className="card-info"
                    variants={fadeInUp}
                  >
                    <Link href="#">
                      <h3 className="text-22-bold">
                        Strategic Brand Positioning{" "}
                      </h3>
                    </Link>
                    <p className="text-md neutral-700">
                      We define your unique voice, message, and core
                      identity so your business stands out.
                    </p>
                  </motion.div>
                </motion.div>
              </div>
              <div className="col-lg-6 col-sm-6">
                <motion.div 
                  className="card-feature-2"
                  ref={card2Ref}
                  variants={cardItem}
                  initial="hidden"
                  animate={card2InView ? "visible" : "hidden"}
                >
                  <div className="card-image">
                    <motion.img 
                      variants={gifAnimation}
                      src="/assets/imgs/page/digitalmarketing/resize_8722642.gif" 
                    />
                  </div>
                  <motion.div 
                    className="card-info"
                    variants={fadeInUp}
                  >
                    <Link href="#">
                      <h3 className="text-22-bold">
                        Logo & Visual Identity{" "}
                      </h3>
                    </Link>
                    <p className="text-md neutral-700">
                      A distinctive, well-crafted logo and cohesive branding
                      elements that make your business instantly
                      recognizable.
                    </p>
                  </motion.div>
                </motion.div>
              </div>
              <div className="col-lg-6 col-sm-6">
                <motion.div 
                  className="card-feature-2"
                  ref={card3Ref}
                  variants={cardItem}
                  initial="hidden"
                  animate={card3InView ? "visible" : "hidden"}
                >
                  <div className="card-image">
                    <motion.img 
                      variants={gifAnimation}
                      src="/assets/imgs/page/digitalmarketing/book.gif" 
                    />
                  </div>
                  <motion.div 
                    className="card-info"
                    variants={fadeInUp}
                  >
                    <Link href="#">
                      <h3 className="text-22-bold">Brand Guidelines</h3>
                    </Link>
                    <p className="text-md neutral-700">
                      A comprehensive set of branding rules to ensure
                      consistency across all platforms.
                    </p>
                  </motion.div>
                </motion.div>
              </div>
              <div className="col-lg-6 col-sm-6">
                <motion.div 
                  className="card-feature-2"
                  ref={card4Ref}
                  variants={cardItem}
                  initial="hidden"
                  animate={card4InView ? "visible" : "hidden"}
                >
                  <div className="card-image">
                    <motion.img 
                      variants={gifAnimation}
                      src="/assets/imgs/page/digitalmarketing/bog.gif" 
                    />
                  </div>
                  <motion.div 
                    className="card-info"
                    variants={fadeInUp}
                  >
                    <Link href="#">
                      <h3 className="text-22-bold">
                        Social Media Branding{" "}
                      </h3>
                    </Link>
                    <p className="text-md neutral-700">
                      Eye-catching, on-brand designs for Instagram,
                      Facebook, LinkedIn, and more.
                    </p>
                  </motion.div>
                </motion.div>
              </div>
              <div className="col-lg-6 col-sm-6">
                <motion.div 
                  className="card-feature-2"
                  ref={card5Ref}
                  variants={cardItem}
                  initial="hidden"
                  animate={card5InView ? "visible" : "hidden"}
                >
                  <div className="card-image">
                    <motion.img 
                      variants={gifAnimation}
                      src="/assets/imgs/page/digitalmarketing/recommendation_15577932.gif" 
                    />
                  </div>
                  <motion.div 
                    className="card-info"
                    variants={fadeInUp}
                  >
                    <Link href="#">
                      <h3 className="text-22-bold">
                        Compelling Storytelling
                      </h3>
                    </Link>
                    <p className="text-md neutral-700">
                      Because great brands don't just sell—they inspire. We
                      craft narratives that connect with your audience on an
                      emotional level.
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
        <div className="container">
          <div className="row">
            {/* Video marketing section - Text first on all devices */}
            <div className="col-lg-5 order-1">
              <div className="box-info-casestudy">
                <motion.h2 
                  ref={videoHeadingRef}
                  initial="hidden"
                  animate={videoHeadingInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="mb-40"
                >
                  VIDEO MARKETING: CAPTURE ATTENTION, DRIVE SALES.
                  <br className="d-none d-lg-block" />
                </motion.h2>
                <motion.p 
                  ref={videoDescRef}
                  initial="hidden"
                  animate={videoDescInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="text-lg neutral-700 mb-40"
                >
                  Consumers today watch before they buy. A well-crafted video
                  has the power to educate, entertain, and convert prospects
                  into customers faster than any other medium.
                </motion.p>                
              </div>
            </div>           
            <div className="col-lg-7 text-center order-2">
              <motion.img
                ref={videoImageRef}
                initial="hidden"
                animate={videoImageInView ? "visible" : "hidden"}
                variants={imageScale}
                src="/assets/imgs/page/digital/22.png"
                alt="Video marketing services Hyderabad - Digital content creation and promotion"
              />
            </div>
            
          </div>
          <div className="mt-50 mb-50">
              <motion.h4
                ref={videoSubHeadingRef}
                initial="hidden"
                animate={videoSubHeadingInView ? "visible" : "hidden"}
                variants={fadeInUp}
              >
                <strong>What We Do:</strong>
              </motion.h4>
              <motion.div 
                className="row mt-50"
                variants={staggerCards}
                initial="hidden"
                animate="visible"
              >
                <div className="col-lg-6 col-sm-6">
                  <motion.div 
                    className="card-feature-2"
                    ref={videoCard1Ref}
                    variants={cardItem}
                    initial="hidden"
                    animate={videoCard1InView ? "visible" : "hidden"}
                  >
                    <div className="card-image">
                      <motion.img
                        variants={gifAnimation}
                        src="/assets/imgs/page/digitalmarketing/ad.gif"
                        alt="Digital advertising services Hyderabad - PPC and social media ads management"
                      />
                    </div>
                    <motion.div 
                      className="card-info"
                      variants={fadeInUp}
                    >
                      <Link href="#">
                        <h3 className="text-22-bold">
                          Ad Films & Commercials
                        </h3>
                      </Link>
                      <p className="text-md neutral-700">
                        High-impact, professionally produced ad films that
                        grab attention in seconds.
                      </p>
                    </motion.div>
                  </motion.div>
                </div>
                <div className="col-lg-6 col-sm-6">
                  <motion.div 
                    className="card-feature-2"
                    ref={videoCard2Ref}
                    variants={cardItem}
                    initial="hidden"
                    animate={videoCard2InView ? "visible" : "hidden"}
                  >
                    <div className="card-image">
                      <motion.img 
                        variants={gifAnimation}
                        src="/assets/imgs/page/digitalmarketing/traning.gif" 
                      />
                    </div>
                    <motion.div 
                      className="card-info"
                      variants={fadeInUp}
                    >
                      <Link href="#">
                        <h3 className="text-22-bold">Explainer Videos</h3>
                      </Link>
                      <p className="text-md neutral-700">
                        Simplify complex ideas and showcase your product's
                        value in the most engaging way.
                      </p>
                    </motion.div>
                  </motion.div>
                </div>
                <div className="col-lg-6 col-sm-6">
                  <motion.div 
                    className="card-feature-2"
                    ref={videoCard3Ref}
                    variants={cardItem}
                    initial="hidden"
                    animate={videoCard3InView ? "visible" : "hidden"}
                  >
                    <div className="card-image">
                      <motion.img 
                        variants={gifAnimation}
                        src="/assets/imgs/page/digitalmarketing/action.gif" 
                      />
                    </div>
                    <motion.div 
                      className="card-info"
                      variants={fadeInUp}
                    >
                      <Link href="#">
                        <h3 className="text-22-bold">
                          Short-Form Content (Reels & Shorts)
                        </h3>
                      </Link>
                      <p className="text-md neutral-700">
                        Perfectly optimized for Instagram, YouTube Shorts, and
                        TikTok to capture quick engagement.
                      </p>
                    </motion.div>
                  </motion.div>
                </div>
                <div className="col-lg-6 col-sm-6">
                  <motion.div 
                    className="card-feature-2"
                    ref={videoCard4Ref}
                    variants={cardItem}
                    initial="hidden"
                    animate={videoCard4InView ? "visible" : "hidden"}
                  >
                    <div className="card-image">
                      <motion.img 
                        variants={gifAnimation}
                        src="/assets/imgs/page/digitalmarketing/brand.gif" 
                      />
                    </div>
                    <motion.div 
                      className="card-info"
                      variants={fadeInUp}
                    >
                      <Link href="#">
                        <h3 className="text-22-bold">
                          Corporate & Brand Films
                        </h3>
                      </Link>
                      <p className="text-md neutral-700">
                        Tell your brand's story in a way that builds
                        credibility and trust.
                      </p>
                    </motion.div>
                  </motion.div>
                </div>
                <div className="col-lg-6 col-sm-6">
                  <motion.div 
                    className="card-feature-2"
                    ref={videoCard5Ref}
                    variants={cardItem}
                    initial="hidden"
                    animate={videoCard5InView ? "visible" : "hidden"}
                  >
                    <div className="card-image">
                      <motion.img 
                        variants={gifAnimation}
                        src="/assets/imgs/page/digitalmarketing/scroll.gif" 
                      />
                    </div>
                    <motion.div 
                      className="card-info"
                      variants={fadeInUp}
                    >
                      <Link href="#">
                        <h3 className="text-22-bold">Testimonial Videos</h3>
                      </Link>
                      <p className="text-md neutral-700">
                        Real customers, real experiences—nothing sells better
                        than authenticity.
                      </p>
                    </motion.div>
                  </motion.div>
                </div>
              </motion.div>
            </div>
        </div>
      </section>
    </>
  );
}