"use client";
import Link from "next/link";
import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Services() {
  const [activeIndex, setActiveIndex] = useState(1);
  const handleOnClick = (index) => {
    setActiveIndex(index);
  };

  // Create refs for different elements to trigger animations when in view
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [headingRef, headingInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [tabsRef, tabsInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [contentRef, contentInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };

  const fadeInUp = {
    hidden: { y: 50, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.6 } },
  };

  const staggerButtons = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const buttonVariant = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.4 } },
  };

  const contentAnimation = {
    hidden: { opacity: 0, x: 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  const listAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const listItemAnimation = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  };

  const imageAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.8 } },
  };

  return (
    <>
      <style jsx global>{`
        /* Add responsive styles for mobile view */
        @media (max-width: 768px) {
          .item-preparing {
            flex-direction: column !important;
          }

          .item-preparing-left,
          .item-preparing-right {
            width: 100% !important;
            max-width: 100% !important;
          }

          .item-preparing-left {
            display: none !important; /* Hide images on mobile */
          }

          .item-preparing-right {
            margin-bottom: 0; /* Remove bottom margin since images are hidden */
          }

          .box-button-preparing {
            flex-wrap: wrap;
            justify-content: center;
          }

          .box-button-preparing .nav li {
            margin-bottom: 10px;
          }
        }
      `}</style>

      <motion.section
        className="section-box "
        ref={sectionRef}
        id="services"
        initial="hidden"
        animate={sectionInView ? "visible" : "hidden"}
        variants={fadeIn}
      >
        <div className="box-preparing-inner" >
          <div className="container">
            <div className="text-center">
              <motion.h2
                className="heading-2 mb-20"
                ref={headingRef}
                initial="hidden"
                animate={headingInView ? "visible" : "hidden"}
                variants={fadeInUp}
              >
                What We Do Best
              </motion.h2>

              <motion.div
                className="box-button-preparing"
                ref={tabsRef}
                initial="hidden"
                animate={tabsInView ? "visible" : "hidden"}
                variants={staggerButtons}
              >
                <ul
                  className="nav nav-tabs justify-content-center"
                  role="tablist"
                >
                  <motion.li
                    onClick={() => handleOnClick(1)}
                    variants={buttonVariant}
                  >
                    <a
                      className={
                        activeIndex === 1
                          ? "btn btn-neutral-100 active"
                          : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                      aria-controls="tab-1"
                      aria-selected="true"
                    >
                      Business Automation
                    </a>
                  </motion.li>
                  <motion.li
                    onClick={() => handleOnClick(2)}
                    variants={buttonVariant}
                  >
                    <a
                      className={
                        activeIndex === 2
                          ? "btn btn-neutral-100 active"
                          : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                      aria-controls="tab-2"
                      aria-selected="false"
                    >
                      WhatsApp Automation
                    </a>
                  </motion.li>
                  <motion.li
                    onClick={() => handleOnClick(3)}
                    variants={buttonVariant}
                  >
                    <a
                      className={
                        activeIndex === 3
                          ? "btn btn-neutral-100 active"
                          : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                      aria-controls="tab-2"
                      aria-selected="false"
                    >
                      360° Sales Automations
                    </a>
                  </motion.li>
                  <motion.li
                    onClick={() => handleOnClick(4)}
                    variants={buttonVariant}
                  >
                    <a
                      className={
                        activeIndex === 4
                          ? "btn btn-neutral-100 active"
                          : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                      aria-controls="tab-2"
                      aria-selected="false"
                    >
                      Website & Funnel Design
                    </a>
                  </motion.li>
                  <motion.li
                    onClick={() => handleOnClick(5)}
                    variants={buttonVariant}
                  >
                    <a
                      className={
                        activeIndex === 5
                          ? "btn btn-neutral-100 active"
                          : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                      aria-controls="tab-2"
                      aria-selected="false"
                    >
                      Performance-Driven Digital Marketing
                    </a>
                  </motion.li>
                </ul>
              </motion.div>
            </div>
            <motion.div
              className="block-group-preparing"
              ref={contentRef}
              initial="hidden"
              animate={contentInView ? "visible" : "hidden"}
              variants={fadeIn}
            >
              <div className="tab-content">
                <motion.div
                  className={
                    activeIndex === 1
                      ? "tab-pane fade show active"
                      : "tab-pane fade"
                  }
                  id="tab-1"
                  initial="hidden"
                  animate={activeIndex === 1 ? "visible" : "hidden"}
                  variants={contentAnimation}
                  key="tab-1"
                >
                  <div className="item-preparing">
                    <motion.div
                      className="item-preparing-left"
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2
                        className="heading-2 mb-20"
                        variants={fadeInUp}
                      >
                        OMX Sync
                      </motion.h2>
                      <motion.p
                        className="text-lg neutral-700"
                        variants={fadeInUp}
                      >
                        Your all-in-one team and operations management tool.
                      </motion.p>
                      <motion.div
                        className="box-list-check"
                        variants={listAnimation}
                      >
                        <ul className="list-check">
                          <motion.li variants={listItemAnimation}>
                            QR & Geo-based Attendance
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Leave Requests & Approvals
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Time Efficiency
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Real-time Task Management
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Internal Workflows & Notifications
                          </motion.li>
                        </ul>
                      </motion.div>
                      <motion.p
                        className="text-lg neutral-700 mb-20"
                        variants={fadeInUp}
                      >
                        Why it matters: No spreadsheets. No confusion. Just
                        clarity, compliance, and productivity.
                      </motion.p>
                      <Link className="btn btn-brand-4" href="/omx-sync">
                        Learn More
                        <svg
                          width={23}
                          height={8}
                          viewBox="0 0 23 8"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                            fill="true"
                          />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </motion.div>
                <motion.div
                  className={
                    activeIndex === 2
                      ? "tab-pane fade show active"
                      : "tab-pane fade"
                  }
                  id="tab-2"
                  initial="hidden"
                  animate={activeIndex === 2 ? "visible" : "hidden"}
                  variants={contentAnimation}
                  key="tab-2"
                >
                  <div className="item-preparing">
                    <motion.div
                      className="item-preparing-left img-2"
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2
                        className="heading-2 mb-20"
                        variants={fadeInUp}
                      >
                        OMX Flow
                      </motion.h2>
                      <motion.p
                        className="text-lg neutral-700"
                        variants={fadeInUp}
                      >
                        Automate conversations, support & commerce 24/7 using
                        AI.
                      </motion.p>
                      <motion.div
                        className="box-list-check"
                        variants={listAnimation}
                      >
                        <ul className="list-check">
                          <motion.li variants={listItemAnimation}>
                            Bulk marketing campaigns & chatbot flows
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Human + AI hybrid chat system
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Payment & CRM integrations
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            WhatsApp API Integration
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            AI-Powered Lead Capture & Follow-up
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Auto-Responders & Drip Sequences
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Funnel Analytics & Conversion Tracking
                          </motion.li>
                          <motion.p
                            className="text-lg neutral-700 mb-20"
                            variants={fadeInUp}
                          >
                            Why it matters: Automate conversations, nurture
                            leads, and turn chats into conversions—at scale
                          </motion.p>
                        </ul>
                        <Link className="btn btn-brand-4" href="/omx-flow">
                          Learn More
                          <svg
                            width={23}
                            height={8}
                            viewBox="0 0 23 8"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                              fill="true"
                            />
                          </svg>
                        </Link>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
                <motion.div
                  className={
                    activeIndex === 3
                      ? "tab-pane fade show active"
                      : "tab-pane fade"
                  }
                  id="tab-3"
                  initial="hidden"
                  animate={activeIndex === 3 ? "visible" : "hidden"}
                  variants={contentAnimation}
                  key="tab-3"
                >
                  <div className="item-preparing">
                    <motion.div
                      className="item-preparing-left img-3"
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2
                        className="heading-2 mb-20"
                        variants={fadeInUp}
                      >
                        OMX Sales
                      </motion.h2>
                      <motion.p
                        className="text-lg neutral-700"
                        variants={fadeInUp}
                      >
                        Your AI-powered command center for leads, ads, and
                        communication.
                      </motion.p>
                      <motion.div
                        className="box-list-check"
                        variants={listAnimation}
                      >
                        <ul className="list-check">
                          <motion.li variants={listItemAnimation}>
                            WhatsApp API, SMS & Email automation
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            CRM, cloud calling & lead tracking
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Ad launcher with AI-based targeting
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            UTM analytics & smart reporting
                          </motion.li>
                        </ul>
                      </motion.div>
                      <motion.p
                        className="text-lg neutral-700 mb-20"
                        variants={fadeInUp}
                      >
                        Why it matters: Every click matters. We make sure every
                        visitor becomes a customer.
                      </motion.p>
                      <Link className="btn btn-brand-4" href="/omx-sales">
                        Learn More
                        <svg
                          width={23}
                          height={8}
                          viewBox="0 0 23 8"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                            fill="true"
                          />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </motion.div>
                <motion.div
                  className={
                    activeIndex === 4
                      ? "tab-pane fade show active"
                      : "tab-pane fade"
                  }
                  id="tab-4"
                  initial="hidden"
                  animate={activeIndex === 4 ? "visible" : "hidden"}
                  variants={contentAnimation}
                  key="tab-4"
                >
                  <div className="item-preparing">
                    <motion.div
                      className="item-preparing-left img-4"
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2
                        className="heading-2 mb-20"
                        variants={fadeInUp}
                      >
                        Website & Funnel Design
                      </motion.h2>
                      <motion.p
                        className="text-lg neutral-700"
                        variants={fadeInUp}
                      >
                        We build sales-ready websites that convert clicks into
                        customers.
                      </motion.p>
                      <motion.div
                        className="box-list-check"
                        variants={listAnimation}
                      >
                        <ul className="list-check mb-20">
                          <motion.li variants={listItemAnimation}>
                            AI-integrated landing pages
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            SEO, speed & mobile optimization
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            WhatsApp & CRM integration
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Funnels with automation built-in
                          </motion.li>
                        </ul>
                        <Link
                          className="btn btn-brand-4"
                          href="/websitedesigning"
                        >
                          Learn More
                          <svg
                            width={23}
                            height={8}
                            viewBox="0 0 23 8"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                              fill="true"
                            />
                          </svg>
                        </Link>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
                <motion.div
                  className={
                    activeIndex === 5
                      ? "tab-pane fade show active"
                      : "tab-pane fade"
                  }
                  id="tab-5"
                  initial="hidden"
                  animate={activeIndex === 5 ? "visible" : "hidden"}
                  variants={contentAnimation}
                  key="tab-5"
                >
                  <div className="item-preparing">
                    <motion.div
                      className="item-preparing-left img-5"
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2
                        className="heading-2 mb-20"
                        variants={fadeInUp}
                      >
                        Performance-Driven Digital Marketing
                      </motion.h2>
                      <motion.p
                        className="text-lg neutral-700"
                        variants={fadeInUp}
                      >
                        From brand to bottom-funnel, we run marketing that
                        sells, not just "gets views."
                      </motion.p>
                      <motion.div
                        className="box-list-check"
                        variants={listAnimation}
                      >
                        <ul className="list-check mb-20">
                          <motion.li variants={listItemAnimation}>
                            Branding & Storytelling
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Paid Ads (Google, Meta, LinkedIn)
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            Video Production & Content Creation
                          </motion.li>
                          <motion.li variants={listItemAnimation}>
                            SEO, Funnel Strategy & Retargeting
                          </motion.li>
                        </ul>
                        <Link
                          className="btn btn-brand-4"
                          href="/digitalmarketing"
                        >
                          Learn More
                          <svg
                            width={23}
                            height={8}
                            viewBox="0 0 23 8"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                              fill="true"
                            />
                          </svg>
                        </Link>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>
    </>
  );
}
