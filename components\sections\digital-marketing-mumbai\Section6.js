"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function Section6() {
  // Refs for scroll-triggered animations
  const sectionRef = useRef(null);
  const imageRef = useRef(null);
  const contentRef = useRef(null);
  const cardsRef = useRef([]);

  // Check if elements are in view to trigger animations
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isImageInView = useInView(imageRef, { once: true, amount: 0.3 });
  const isContentInView = useInView(contentRef, { once: true, amount: 0.3 });

  // Variants for animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.5,
        staggerChildren: 0.2
      } 
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 } 
    }
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.7 } 
    }
  };

  const headingVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 } 
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 } 
    }
  };

  return (
    <>
      <motion.section 
        ref={sectionRef}
        className="section-box wow box-case-study-2 box-client-2"
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <div className="container">
          <div className="row">
            <div className="col-lg-6 text-center mb-40 order-2 order-lg-1">
              <motion.img
                ref={imageRef}
                src="/assets/imgs/page/digitalmarketing/ads.png"
                alt="Digital advertising solutions Mumbai - Facebook Instagram Google LinkedIn ads management"
                variants={imageVariants}
                initial="hidden"
                animate={isImageInView ? "visible" : "hidden"}
              />
            </div>
            <motion.div 
              className="col-lg-6 mb-40 order-1 order-lg-2"
              ref={contentRef}
              variants={containerVariants}
              initial="hidden"
              animate={isContentInView ? "visible" : "hidden"}
            >
              <div className="box-padding-left-50">
                <motion.h2 
                  className="heading-2 mb-20"
                  variants={headingVariants}
                >
                  PAID ADVERTISING: GET IN FRONT OF THE RIGHT AUDIENCE.
                </motion.h2>
                <motion.p 
                  className="text-lg neutral-700 mb-40"
                  variants={itemVariants}
                >
                  We don't believe in random ad spending. We believe in
                  scientific, results-driven advertising where every rupee is
                  accounted for.
                </motion.p>
                
              </div>
            </motion.div>
          </div>
          <motion.h4 variants={itemVariants}><strong>What We Do:</strong></motion.h4>
                <motion.div 
                  className="row mt-50"
                  variants={containerVariants}
                >
                  {[
                    {
                      image: "/assets/imgs/page/omx-flow/ad.gif",
                      title: "Facebook & Instagram Ads",
                      description: "Hyper-targeted campaigns designed for maximum engagement and conversions."
                    },
                    {
                      image: "/assets/imgs/page/digitalmarketing/ad.gif",
                      title: "Google Ads & YouTube Ads",
                      description: "Get in front of high-intent buyers exactly when they're searching."
                    },
                    {
                      image: "/assets/imgs/page/digitalmarketing/b2b.gif",
                      title: "LinkedIn & B2B Advertising",
                      description: "Capture the attention of key decision-makers in the right industries."
                    },
                    {
                      image: "/assets/imgs/page/digitalmarketing/engage.gif",
                      title: "Retargeting & Funnel-Based Strategies",
                      description: "Bring back visitors who didn't convert the first time."
                    },
                    {
                      image: "/assets/imgs/page/omx-sales/optimize.gif",
                      title: "Conversion Rate Optimization (CRO)",
                      description: "Optimize landing pages and ads for higher ROI."
                    }
                  ].map((card, index) => (
                    <div className="col-lg-6 col-sm-6" key={index}>
                      <CardFeature 
                        image={card.image}
                        title={card.title}
                        description={card.description}
                        index={index}
                      />
                    </div>
                  ))}
                </motion.div>
        </div>
      </motion.section>
    </>
  );
}

// Separate component for card with its own scroll animation
function CardFeature({ image, title, description, index }) {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });
  
  return (
    <motion.div 
      className="card-feature-2"
      ref={cardRef}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
    >
      <motion.div 
        className="card-image"
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <img src={image} alt={title} />
      </motion.div>
      <div className="card-info">
        <Link href="#">
          <motion.h3 
            className="text-22-bold"
            whileHover={{ color: "#3056D3" }}
          >
            {title}
          </motion.h3>
        </Link>
        <motion.p 
          className="text-md neutral-700"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
        >
          {description}
        </motion.p>
      </div>
    </motion.div>
  );
}