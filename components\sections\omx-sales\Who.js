"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import VideoPopup from "@/components/elements/VideoPopup";
import Link from "next/link";
import SalesCTA from "@/components/elements/SalesCTA";

export default function Who() {
  // Refs for scroll detection
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef(null);
  const buttonsRef = useRef(null);

  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isTitleInView = useInView(titleRef, { once: true, amount: 0.5 });
  const isCardsInView = useInView(cardsRef, { once: true, amount: 0.2 });
  const isButtonsInView = useInView(buttonsRef, { once: true, amount: 0.5 });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  };

  const titleAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.7, ease: "easeOut" },
    },
  };

  const staggerCards = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const cardAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  const buttonAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
        delay: 0.2,
      },
    },
  };

  return (
    <>
      <motion.section
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={fadeIn}
        className="section-box wow animate__animated animate__fadeIn box-what-we-do"
      >
        <div className="container ">
          <motion.div
            ref={titleRef}
            initial="hidden"
            animate={isTitleInView ? "visible" : "hidden"}
            variants={titleAnimation}
            className="text-center"
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="mt-4">
              <Link className="btn btn-brand-5" href="#">
                Our Users
              </Link>
            </motion.div>
            <h2 className="heading-1 neutral-0 mt-15 mb-65">
              Who is OMX Sales For?
            </h2>
          </motion.div>

          <motion.div
            ref={cardsRef}
            initial="hidden"
            animate={isCardsInView ? "visible" : "hidden"}
            variants={staggerCards}
            className="box-list-we-do"
          >
            <div className="row">
              <div className="col-xl-3 col-lg-5">
                <motion.div
                  variants={cardAnimation}
                  whileHover={{ y: -10, transition: { duration: 0.2 } }}
                  className="card-features-6 hover-up"
                >
                  <div className="card-image">
                    <img
                          src="/assets/imgs/page/omx-sales/Education & Coaching.png"
                          alt="Nivia"
                        />
                  </div>
                  <div className="card-info">
                    <h6>Coaches & Consultants</h6>
                    <p className="text-sm neutral-400">
                      Generate & nurture high-quality leads.
                    </p>
                    <img
                      className="mt-25"
                      src="/assets/imgs/page/homepage4/img-web.png"
                      alt="Nivia"
                    />
                  </div>
                </motion.div>
              </div>
              <div className="col-xl-4 col-lg-7">
                <motion.div
                  variants={cardAnimation}
                  whileHover={{ y: -10, transition: { duration: 0.2 } }}
                  className="card-features-6 hover-up"
                >
                  <div className="card-image"><img
                          src="/assets/imgs/page/omx-sales/Marketing & Sales Agencies.png"
                          alt="Nivia"
                        /></div>
                  <div className="card-info">
                    <h6>Marketing & Sales Agencies</h6>
                    <p className="text-sm neutral-400">
                      Run ad campaigns & track every lead source.
                    </p>
                  </div>
                </motion.div>
                <motion.div
                  variants={cardAnimation}
                  whileHover={{ y: -10, transition: { duration: 0.2 } }}
                  className="card-features-6 card-arrow-2 hover-up"
                >
                  <div className="card-image"><img
                          src="/assets/imgs/page/omx-sales/SaaS & B2B Companies.png"
                          alt="Nivia"
                        /></div>
                  <div className="card-info">
                    <h6>SaaS & B2B Companies</h6>
                    <p className="text-sm neutral-400">
                      Automate lead response & sales team workflows.
                    </p>
                  </div>
                </motion.div>
              </div>
              <div className="col-xl-5 col-lg-12">
                <div className="row">
                  <div className="col-lg-12">
                    <motion.div
                      variants={cardAnimation}
                      whileHover={{ y: -10, transition: { duration: 0.2 } }}
                      className="card-features-6 card-arrow-3 hover-up"
                    >
                      <div className="card-image">
                      <img
                          src="/assets/imgs/page/omx-sales/Real Estate & High-Ticket Businesses.png"
                          alt="Nivia"
                        />
                      </div>
                      <div className="card-info">
                        <div className="card-info-inner">
                          <div className="card-info-left">
                            <h6>Real Estate & High-Ticket Businesses </h6>
                            <p className="text-sm neutral-400">
                              Auto-manage calls, leads & WhatsApp follow-ups.
                            </p>
                          </div>
                          <div className="card-info-right">
                            {/* Button placeholder */}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                  <div className="col-lg-12">
                    <motion.div
                      variants={cardAnimation}
                      whileHover={{ y: -10, transition: { duration: 0.2 } }}
                      className="card-features-6 card-arrow-3 hover-up"
                    >
                      <div className="card-image">
                      <img
                          src="/assets/imgs/page/omx-sales/Finance, Trading & Investment Firms.png"
                          alt="Nivia"
                        />
                      </div>
                      <div className="card-info">
                        <div className="card-info-inner">
                          <div className="card-info-left">
                            <h6> Finance, Trading & Investment Firms </h6>
                            <p className="text-sm neutral-400">
                              Engage leads via WhatsApp, SMS & calls.
                            </p>
                          </div>
                          <div className="card-info-right">
                            {/* Button placeholder */}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            ref={buttonsRef}
            initial="hidden"
            animate={isButtonsInView ? "visible" : "hidden"}
            variants={buttonAnimation}
            className="box-buttons justify-content-center mt-35"
          >
            <Link className="btn btn-brand-4-medium hover-up" href="/form">
                            Book a Demo
                            <svg
                              width={22}
                              height={22}
                              viewBox="0 0 22 22"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                fill="true"
                              ></path>
                            </svg>
             </Link>
            {/* <SalesCTA/> */}
            {/* <VideoPopup /> */}
          </motion.div>
        </div>
      </motion.section>
    </>
  );
}
