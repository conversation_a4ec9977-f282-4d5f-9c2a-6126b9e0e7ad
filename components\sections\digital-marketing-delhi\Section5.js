"use client";
import DigitalCTA from "@/components/elements/DigitalCTA";
import Link from "next/link";
export default function Section5() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-case-study-2 box-client-2">
        <div className="container">
          <div className="block-case-study ">
            <div className="row align-items-center mb-30">
              <div className="col-lg-7 mb-30">
                {/* <Link className="btn btn-border-linear-2 mb-35" href="#"><span>Case
                                Study</span></Link> */}
                <h2 className="heading-1 text-linear-3 mb-35 ">
                  PERFORMANCE MARKETING: SPEND SMART. SELL MORE.
                </h2>
                <p className="text-lg neutral-0 mb-40">
                  Most businesses waste money on ads that don’t convert. Why?
                  Because they lack a performance-driven strategy.
                </p>
                <p className="text-lg neutral-0 mb-40">
                  At OMX Digital, we take the guesswork out of advertising and
                  ensure that every campaign is backed by data, AI-powered
                  insights, and real-time optimization to maximize returns.
                </p>
                <p className="text-lg neutral-0 mb-40">
                  We focus on precision targeting, retargeting, and funnel-based
                  ad strategies to turn cold traffic into hot leads and paying
                  customers.
                </p>
              </div>
              <div className="col-lg-5 mb-30">
              <img
                      src="/assets/imgs/page/digitalmarketing/pm.png"
                      alt="Omxdigital"
                    />
              </div>
            </div>
            <div className="d-flex justify-content-center">
              {/* <DigitalCTA/> */}
              <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                                            Book Demo
                                                            <svg
                                                              width={22}
                                                              height={22}
                                                              viewBox="0 0 22 22"
                                                              fill="none"
                                                              xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                              <path
                                                                d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                                fill="true"
                                                              ></path>
                                                            </svg>
                              </Link>
              </div>
            
          </div>
          <br />
        </div>
      </section>
    </>
  );
}
